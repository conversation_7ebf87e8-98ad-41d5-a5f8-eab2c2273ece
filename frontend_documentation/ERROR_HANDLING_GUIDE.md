# Error Handling Guide

## Overview

This guide provides comprehensive error handling strategies for frontend applications integrating with the Pantry Pal API. The API uses standardized error responses and HTTP status codes.

## Error Response Structure

### Standard Error Response

```typescript
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ErrorInfo;
  message?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}

interface ErrorInfo {
  code: string;
  message: string;
  details?: Record<string, any>;
}
```

### Error Response Examples

#### Validation Error

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": {
      "fields": {
        "email": "Invalid email format",
        "password": "Password must be at least 8 characters"
      }
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

#### Authentication Error

```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

#### Business Rule Violation

```json
{
  "success": false,
  "error": {
    "code": "BUSINESS_RULE_VIOLATION",
    "message": "Cannot delete pantry with active inventory",
    "details": {
      "rule": "pantry_deletion_with_inventory",
      "inventory_count": 15
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

## Error Codes Reference

### Authentication & Authorization

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `UNAUTHORIZED` | 401 | Authentication required | Redirect to login |
| `FORBIDDEN` | 403 | Insufficient permissions | Show permission error |
| `TOKEN_EXPIRED` | 401 | Access token expired | Refresh token automatically |
| `INVALID_CREDENTIALS` | 401 | Invalid login credentials | Show login error |

### Validation Errors

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `VALIDATION_FAILED` | 400 | Request validation failed | Show field errors |
| `INVALID_INPUT` | 400 | Invalid input format | Show input error |
| `MISSING_REQUIRED_FIELD` | 400 | Required field missing | Highlight missing fields |

### Resource Errors

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `NOT_FOUND` | 404 | Resource not found | Show not found message |
| `CONFLICT` | 409 | Resource conflict | Show conflict resolution |
| `DUPLICATE_RESOURCE` | 409 | Resource already exists | Show duplicate error |

### Business Logic Errors

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `BUSINESS_RULE_VIOLATION` | 422 | Business rule violated | Show business rule error |
| `INSUFFICIENT_INVENTORY` | 422 | Not enough inventory | Show quantity error |
| `PANTRY_ACCESS_DENIED` | 403 | No access to pantry | Show access denied |

### System Errors

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `INTERNAL_SERVER_ERROR` | 500 | Server error | Show generic error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable | Show retry message |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests | Show rate limit message |

## Error Handling Implementation

### Error Handler Class

```typescript
interface ErrorHandlerConfig {
  showToasts: boolean;
  logErrors: boolean;
  retryAttempts: number;
  retryDelay: number;
}

class ErrorHandler {
  private config: ErrorHandlerConfig;
  private retryQueue: Map<string, number> = new Map();

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      showToasts: true,
      logErrors: true,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config,
    };
  }

  handleError(error: any, context?: string): ErrorInfo | null {
    const errorInfo = this.extractErrorInfo(error);
    
    if (this.config.logErrors) {
      this.logError(error, errorInfo, context);
    }

    if (this.config.showToasts) {
      this.showErrorToast(errorInfo);
    }

    // Handle specific error types
    switch (errorInfo.code) {
      case 'UNAUTHORIZED':
      case 'TOKEN_EXPIRED':
        this.handleAuthError();
        break;
      case 'VALIDATION_FAILED':
        return this.handleValidationError(errorInfo);
      case 'RATE_LIMIT_EXCEEDED':
        this.handleRateLimitError(errorInfo);
        break;
      case 'SERVICE_UNAVAILABLE':
        this.handleServiceUnavailableError(errorInfo);
        break;
      default:
        this.handleGenericError(errorInfo);
    }

    return errorInfo;
  }

  private extractErrorInfo(error: any): ErrorInfo {
    // Network error
    if (!error.response) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed. Please check your internet connection.',
        details: { originalError: error.message }
      };
    }

    // API error response
    if (error.response?.data?.error) {
      return error.response.data.error;
    }

    // HTTP error without API error structure
    return {
      code: 'HTTP_ERROR',
      message: `HTTP ${error.response.status}: ${error.response.statusText}`,
      details: { status: error.response.status }
    };
  }

  private logError(originalError: any, errorInfo: ErrorInfo, context?: string): void {
    console.error('Error occurred:', {
      context,
      errorInfo,
      originalError,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });

    // Send to error tracking service (e.g., Sentry)
    if (window.Sentry) {
      window.Sentry.captureException(originalError, {
        tags: { context },
        extra: { errorInfo },
      });
    }
  }

  private showErrorToast(errorInfo: ErrorInfo): void {
    // Implementation depends on your toast library
    // Example with react-hot-toast:
    // toast.error(this.getUserFriendlyMessage(errorInfo));
  }

  private handleAuthError(): void {
    // Clear tokens and redirect to login
    TokenManager.clearTokens();
    window.location.href = '/login';
  }

  private handleValidationError(errorInfo: ErrorInfo): ErrorInfo {
    // Return validation errors for form handling
    return errorInfo;
  }

  private handleRateLimitError(errorInfo: ErrorInfo): void {
    const retryAfter = errorInfo.details?.retry_after || 60;
    this.showErrorToast({
      ...errorInfo,
      message: `Too many requests. Please wait ${retryAfter} seconds before trying again.`
    });
  }

  private handleServiceUnavailableError(errorInfo: ErrorInfo): void {
    this.showErrorToast({
      ...errorInfo,
      message: 'Service is temporarily unavailable. Please try again later.'
    });
  }

  private handleGenericError(errorInfo: ErrorInfo): void {
    this.showErrorToast({
      ...errorInfo,
      message: 'An unexpected error occurred. Please try again.'
    });
  }

  getUserFriendlyMessage(errorInfo: ErrorInfo): string {
    const friendlyMessages: Record<string, string> = {
      'NETWORK_ERROR': 'Please check your internet connection and try again.',
      'UNAUTHORIZED': 'Please log in to continue.',
      'FORBIDDEN': 'You don\'t have permission to perform this action.',
      'NOT_FOUND': 'The requested item could not be found.',
      'VALIDATION_FAILED': 'Please check your input and try again.',
      'BUSINESS_RULE_VIOLATION': 'This action is not allowed.',
      'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait a moment.',
      'INTERNAL_SERVER_ERROR': 'Something went wrong. Please try again later.',
    };

    return friendlyMessages[errorInfo.code] || errorInfo.message;
  }

  async retryOperation<T>(
    operation: () => Promise<T>,
    operationId: string,
    maxRetries?: number
  ): Promise<T> {
    const retries = maxRetries || this.config.retryAttempts;
    const currentRetry = this.retryQueue.get(operationId) || 0;

    try {
      const result = await operation();
      this.retryQueue.delete(operationId);
      return result;
    } catch (error) {
      if (currentRetry < retries && this.shouldRetry(error)) {
        this.retryQueue.set(operationId, currentRetry + 1);
        
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelay * Math.pow(2, currentRetry))
        );
        
        return this.retryOperation(operation, operationId, maxRetries);
      }

      this.retryQueue.delete(operationId);
      throw error;
    }
  }

  private shouldRetry(error: any): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'SERVICE_UNAVAILABLE',
      'INTERNAL_SERVER_ERROR'
    ];

    const errorInfo = this.extractErrorInfo(error);
    return retryableCodes.includes(errorInfo.code);
  }
}

export const errorHandler = new ErrorHandler();
```

### React Error Boundary

```typescript
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error boundary caught an error:', error, errorInfo);
    
    // Log to error tracking service
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        contexts: { react: errorInfo },
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button onClick={() => window.location.reload()}>
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### API Client Error Integration

```typescript
import axios, { AxiosError } from 'axios';
import { errorHandler } from './ErrorHandler';

// Extend the API client with error handling
class APIClientWithErrorHandling extends APIClient {
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      return await super.get<T>(url, config);
    } catch (error) {
      const errorInfo = errorHandler.handleError(error, `GET ${url}`);
      throw new APIError(errorInfo);
    }
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      return await super.post<T>(url, data, config);
    } catch (error) {
      const errorInfo = errorHandler.handleError(error, `POST ${url}`);
      throw new APIError(errorInfo);
    }
  }

  // Similar for put, delete, etc.
}

class APIError extends Error {
  constructor(public errorInfo: ErrorInfo) {
    super(errorInfo.message);
    this.name = 'APIError';
  }
}

export const apiClient = new APIClientWithErrorHandling('http://localhost:8080/api/v1');

## Form Validation Error Handling

### React Hook Form Integration

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export const LoginForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isSubmitting }
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      await authService.login(data);
    } catch (error) {
      if (error instanceof APIError && error.errorInfo.code === 'VALIDATION_FAILED') {
        // Set server validation errors on form fields
        const fieldErrors = error.errorInfo.details?.fields || {};
        Object.entries(fieldErrors).forEach(([field, message]) => {
          setError(field as keyof LoginFormData, {
            type: 'server',
            message: message as string,
          });
        });
      } else {
        // Handle other errors
        setError('root', {
          type: 'server',
          message: errorHandler.getUserFriendlyMessage(error.errorInfo),
        });
      }
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        <input
          {...register('email')}
          type="email"
          placeholder="Email"
        />
        {errors.email && <span className="error">{errors.email.message}</span>}
      </div>

      <div>
        <input
          {...register('password')}
          type="password"
          placeholder="Password"
        />
        {errors.password && <span className="error">{errors.password.message}</span>}
      </div>

      {errors.root && <div className="error">{errors.root.message}</div>}

      <button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
};
```

### Custom Hook for API Operations

```typescript
import { useState, useCallback } from 'react';

interface UseAPIOperationResult<T> {
  data: T | null;
  loading: boolean;
  error: ErrorInfo | null;
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
}

export function useAPIOperation<T>(
  operation: (...args: any[]) => Promise<T>,
  options: {
    onSuccess?: (data: T) => void;
    onError?: (error: ErrorInfo) => void;
    showErrorToast?: boolean;
  } = {}
): UseAPIOperationResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ErrorInfo | null>(null);

  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await operation(...args);
      setData(result);
      options.onSuccess?.(result);
      return result;
    } catch (err) {
      const errorInfo = errorHandler.handleError(err, operation.name);
      setError(errorInfo);
      options.onError?.(errorInfo);
      return null;
    } finally {
      setLoading(false);
    }
  }, [operation, options]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return { data, loading, error, execute, reset };
}

// Usage example
export const PantryList: React.FC = () => {
  const {
    data: pantries,
    loading,
    error,
    execute: loadPantries
  } = useAPIOperation(pantryService.getPantries, {
    onError: (error) => {
      if (error.code !== 'UNAUTHORIZED') {
        toast.error('Failed to load pantries');
      }
    }
  });

  useEffect(() => {
    loadPantries();
  }, [loadPantries]);

  if (loading) return <div>Loading pantries...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {pantries?.map(pantry => (
        <div key={pantry.id}>{pantry.name}</div>
      ))}
    </div>
  );
};
```

## Error Recovery Strategies

### Retry with Exponential Backoff

```typescript
class RetryableOperation {
  static async execute<T>(
    operation: () => Promise<T>,
    options: {
      maxRetries?: number;
      baseDelay?: number;
      maxDelay?: number;
      shouldRetry?: (error: any) => boolean;
    } = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      maxDelay = 10000,
      shouldRetry = (error) => {
        const errorInfo = errorHandler.extractErrorInfo(error);
        return ['NETWORK_ERROR', 'SERVICE_UNAVAILABLE'].includes(errorInfo.code);
      }
    } = options;

    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries || !shouldRetry(error)) {
          throw error;
        }

        const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }
}
```

### Offline Support

```typescript
class OfflineManager {
  private static isOnline = navigator.onLine;
  private static pendingOperations: Array<{
    id: string;
    operation: () => Promise<any>;
    timestamp: number;
  }> = [];

  static init() {
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }

  static isConnected(): boolean {
    return this.isOnline;
  }

  static async executeWhenOnline<T>(
    operation: () => Promise<T>,
    operationId: string
  ): Promise<T> {
    if (this.isOnline) {
      return operation();
    }

    // Queue operation for when online
    return new Promise((resolve, reject) => {
      this.pendingOperations.push({
        id: operationId,
        operation: async () => {
          try {
            const result = await operation();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        },
        timestamp: Date.now(),
      });
    });
  }

  private static async handleOnline() {
    this.isOnline = true;

    // Execute pending operations
    const operations = [...this.pendingOperations];
    this.pendingOperations = [];

    for (const { operation } of operations) {
      try {
        await operation();
      } catch (error) {
        console.error('Failed to execute pending operation:', error);
      }
    }
  }

  private static handleOffline() {
    this.isOnline = false;
  }
}
```

## Error Monitoring and Analytics

### Error Tracking Setup

```typescript
interface ErrorTrackingConfig {
  dsn: string;
  environment: string;
  userId?: string;
  userEmail?: string;
}

class ErrorTracking {
  static init(config: ErrorTrackingConfig) {
    // Initialize Sentry or similar service
    if (window.Sentry) {
      window.Sentry.init({
        dsn: config.dsn,
        environment: config.environment,
        beforeSend: (event) => {
          // Filter out sensitive information
          if (event.request?.headers?.Authorization) {
            delete event.request.headers.Authorization;
          }
          return event;
        },
      });

      if (config.userId) {
        window.Sentry.setUser({
          id: config.userId,
          email: config.userEmail,
        });
      }
    }
  }

  static captureError(error: Error, context?: Record<string, any>) {
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        extra: context,
      });
    }
  }

  static captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info') {
    if (window.Sentry) {
      window.Sentry.captureMessage(message, level);
    }
  }
}
```

## Testing Error Handling

### Unit Tests for Error Handler

```typescript
import { errorHandler } from './ErrorHandler';

describe('ErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle network errors', () => {
    const networkError = new Error('Network Error');
    networkError.code = 'NETWORK_ERROR';

    const result = errorHandler.handleError(networkError);

    expect(result.code).toBe('NETWORK_ERROR');
    expect(result.message).toContain('Network connection failed');
  });

  it('should handle API validation errors', () => {
    const apiError = {
      response: {
        data: {
          error: {
            code: 'VALIDATION_FAILED',
            message: 'Validation failed',
            details: {
              fields: {
                email: 'Invalid email format'
              }
            }
          }
        }
      }
    };

    const result = errorHandler.handleError(apiError);

    expect(result.code).toBe('VALIDATION_FAILED');
    expect(result.details.fields.email).toBe('Invalid email format');
  });

  it('should retry operations with exponential backoff', async () => {
    let attempts = 0;
    const operation = jest.fn().mockImplementation(() => {
      attempts++;
      if (attempts < 3) {
        throw new Error('Service unavailable');
      }
      return 'success';
    });

    const result = await errorHandler.retryOperation(operation, 'test-op');

    expect(result).toBe('success');
    expect(attempts).toBe(3);
  });
});
```

## Best Practices

### 1. Error Message Guidelines

* Use clear, user-friendly language
* Avoid technical jargon
* Provide actionable guidance when possible
* Be specific about what went wrong

### 2. Error Logging

* Log all errors with sufficient context
* Include user ID, request ID, and timestamp
* Don't log sensitive information (passwords, tokens)
* Use structured logging for better analysis

### 3. User Experience

* Show loading states during operations
* Provide clear feedback for errors
* Offer retry options for transient errors
* Gracefully degrade functionality when possible

### 4. Error Recovery

* Implement automatic retry for network errors
* Cache data for offline scenarios
* Provide manual refresh options
* Clear error states when appropriate

### 5. Testing

* Test error scenarios in unit tests
* Use error boundary testing
* Test network failure scenarios
* Validate error message display
```
