# API Integration Guide

## Overview

This guide provides comprehensive information for integrating with the Pantry Pal API. The API follows RESTful principles and uses JWT authentication with standardized JSON responses.

## Base Configuration

```typescript
const API_CONFIG = {
  baseURL: 'http://localhost:8080/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
};
```

## Authentication

### JWT Token Management

The API uses JWT Bearer tokens for authentication with automatic refresh capability.

```typescript
interface TokenPair {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: 'Bearer';
}

interface AuthResponse {
  success: boolean;
  data: {
    user: UserProfile;
    tokens: TokenPair;
  };
  message: string;
  timestamp: string;
}
```

### Authentication Endpoints

#### Register User

```http
POST /auth/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "confirm_password": "securepassword123",
  "first_name": "<PERSON>",
  "last_name": "Do<PERSON>"
}
```

#### Login User

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

#### Refresh Token

```http
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Logout

```http
POST /auth/logout
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Standard Response Format

All API responses follow a consistent structure:

```typescript
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ErrorInfo;
  message?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}

interface ErrorInfo {
  code: string;
  message: string;
  details?: Record<string, any>;
}

interface PaginatedResponse<T = any> extends APIResponse<T> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}
```

## Core API Endpoints

### User Management

#### Get User Profile

```http
GET /users/profile
Authorization: Bearer {access_token}
```

#### Update User Profile

```http
PUT /users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "profile_picture_url": "https://example.com/avatar.jpg"
}
```

#### Change Password

```http
POST /users/change-password
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "current_password": "oldpassword",
  "new_password": "newpassword123",
  "confirm_password": "newpassword123"
}
```

### Pantry Management

#### Create Pantry

```http
POST /pantries
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "Family Kitchen",
  "description": "Main family pantry and refrigerator"
}
```

#### Get User Pantries

```http
GET /pantries
Authorization: Bearer {access_token}
```

#### Get Pantry Details

```http
GET /pantries/{pantryId}
Authorization: Bearer {access_token}
```

#### Update Pantry

```http
PUT /pantries/{pantryId}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "Updated Pantry Name",
  "description": "Updated description"
}
```

#### Delete Pantry

```http
DELETE /pantries/{pantryId}
Authorization: Bearer {access_token}
```

### Pantry Membership

#### Invite Member

```http
POST /pantries/{pantryId}/members
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role": "editor"
}
```

#### Get Pantry Members

```http
GET /pantries/{pantryId}/members
Authorization: Bearer {access_token}
```

#### Update Member Role

```http
PUT /pantries/{pantryId}/members/{memberId}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "role": "admin"
}
```

#### Remove Member

```http
DELETE /pantries/{pantryId}/members/{memberId}
Authorization: Bearer {access_token}
```

### Inventory Management

#### Create Inventory Item

```http
POST /pantries/{pantryId}/inventory
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "product_variant_id": "uuid",
  "quantity": 2.5,
  "unit_of_measure_id": "uuid",
  "location_id": "uuid",
  "expiration_date": "2024-12-31T00:00:00Z",
  "purchase_date": "2024-01-15T00:00:00Z",
  "purchase_price": 4.99,
  "notes": "Organic brand"
}
```

#### Get Pantry Inventory

```http
GET /pantries/{pantryId}/inventory?page=1&limit=20&search=milk&location_id=uuid&category_id=uuid&expiring_soon=true
Authorization: Bearer {access_token}
```

#### Update Inventory Item

```http
PUT /pantries/{pantryId}/inventory/{itemId}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "quantity": 1.5,
  "expiration_date": "2024-12-25T00:00:00Z",
  "notes": "Updated notes"
}
```

#### Consume Inventory Item

```http
POST /pantries/{pantryId}/inventory/{itemId}/consume
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "consumed_quantity": 0.5,
  "notes": "Used for breakfast"
}
```

### Product Catalog

#### Get Categories

```http
GET /catalog/categories?page=1&limit=50
Authorization: Bearer {access_token}
```

#### Get Products

```http
GET /catalog/products?page=1&limit=20&search=milk&category_id=uuid
Authorization: Bearer {access_token}
```

#### Get Product Details

```http
GET /catalog/products/{productId}
Authorization: Bearer {access_token}
```

#### Get Product Variants

```http
GET /catalog/products/{productId}/variants
Authorization: Bearer {access_token}
```

### Recipe Management

#### Create Recipe

```http
POST /recipes
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "title": "Chocolate Chip Cookies",
  "description": "Classic homemade cookies",
  "cuisine": "American",
  "category": "Dessert",
  "difficulty": "easy",
  "prep_time": 15,
  "cook_time": 12,
  "servings": 24,
  "is_public": false,
  "ingredients": [
    {
      "product_variant_id": "uuid",
      "quantity": 2.25,
      "unit_of_measure_id": "uuid",
      "notes": "All-purpose flour"
    }
  ],
  "instructions": [
    {
      "step_number": 1,
      "instruction": "Preheat oven to 375°F",
      "duration": 5
    }
  ],
  "nutrition": {
    "calories": 150,
    "protein": 2.0,
    "carbohydrates": 20.0,
    "fat": 7.0
  }
}
```

#### Get User Recipes

```http
GET /recipes?page=1&limit=20&search=cookie&cuisine=American&difficulty=easy&is_favorite=true
Authorization: Bearer {access_token}
```

#### Check Recipe Ingredients

```http
POST /recipes/{recipeId}/check-ingredients
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "pantry_id": "uuid"
}
```

### Shopping Lists

#### Generate Shopping List

```http
POST /pantries/{pantryId}/inventory/shopping-list
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "low_stock_threshold": 1.0,
  "include_expired": true,
  "recipe_ids": ["uuid1", "uuid2"]
}
```

## Error Handling

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_FAILED` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `CONFLICT` | 409 | Resource conflict |
| `BUSINESS_RULE_VIOLATION` | 422 | Business rule violation |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `INTERNAL_SERVER_ERROR` | 500 | Server error |

### Error Response Example

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": {
      "fields": {
        "email": "Invalid email format",
        "password": "Password must be at least 8 characters"
      }
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

## Idempotency

For critical operations, include an idempotency key to prevent duplicate operations:

```http
POST /pantries/{pantryId}/inventory
Authorization: Bearer {access_token}
Idempotency-Key: unique-operation-key-123
Content-Type: application/json
```

## Rate Limiting

The API implements rate limiting. Monitor these headers:

* `X-RateLimit-Limit`: Request limit per window
* `X-RateLimit-Remaining`: Remaining requests in current window
* `X-RateLimit-Reset`: Time when the rate limit resets

## Pagination

List endpoints support pagination with these parameters:

* `page`: Page number (default: 1)
* `limit`: Items per page (default: 10, max: 100)
* `sort_by`: Sort field
* `sort_order`: `asc` or `desc`

## Bulk Operations

### Bulk Create Inventory Items

```http
POST /pantries/{pantryId}/inventory/bulk
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "items": [
    {
      "product_variant_id": "uuid1",
      "quantity": 2.0,
      "unit_of_measure_id": "uuid",
      "location_id": "uuid"
    },
    {
      "product_variant_id": "uuid2",
      "quantity": 1.5,
      "unit_of_measure_id": "uuid",
      "location_id": "uuid"
    }
  ]
}
```

### Bulk Update Inventory Items

```http
PUT /inventory/bulk
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "updates": [
    {
      "id": "uuid1",
      "quantity": 1.0
    },
    {
      "id": "uuid2",
      "expiration_date": "2024-12-31T00:00:00Z"
    }
  ]
}
```

### Bulk Consume Items

```http
POST /inventory/bulk/consume
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "consumptions": [
    {
      "id": "uuid1",
      "consumed_quantity": 0.5
    },
    {
      "id": "uuid2",
      "consumed_quantity": 1.0
    }
  ]
}
```

### Consume Recipe Ingredients

```http
POST /pantries/{pantryId}/inventory/recipe/consume
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "recipe_id": "uuid",
  "servings": 4,
  "notes": "Dinner preparation"
}
```

## Expiration Tracking

### Track Expiring Items

```http
POST /pantries/{pantryId}/expiration/track
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "alert_days": 3,
  "critical_days": 1,
  "category_filters": ["dairy", "meat"]
}
```

### Configure Expiration Alerts

```http
POST /pantries/{pantryId}/expiration/alerts
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "enabled": true,
  "alert_days": 3,
  "warning_days": 7,
  "critical_days": 1,
  "min_value": 1.0,
  "channels": [
    {
      "type": "email",
      "enabled": true,
      "config": {
        "email": "<EMAIL>"
      }
    },
    {
      "type": "push",
      "enabled": true
    }
  ],
  "quiet_hours": {
    "enabled": true,
    "start_time": "22:00",
    "end_time": "08:00",
    "timezone": "America/New_York"
  },
  "category_filters": ["dairy", "meat", "produce"]
}
```

## Pantry Locations

### Create Location

```http
POST /pantries/{pantryId}/locations
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "Freezer",
  "description": "Main freezer compartment",
  "location_type": "freezer"
}
```

### Get Pantry Locations

```http
GET /pantries/{pantryId}/locations
Authorization: Bearer {access_token}
```

## Units of Measure

### Get Units

```http
GET /catalog/units?page=1&limit=50
Authorization: Bearer {access_token}
```

### Create Derived Unit

```http
POST /catalog/units/derived
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "tablespoon",
  "symbol": "tbsp",
  "base_unit_id": "uuid",
  "conversion_factor": 0.0625
}
```

## WebSocket Integration (Future)

For real-time updates, the API will support WebSocket connections:

```typescript
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onmessage = (event) => {
  const notification = JSON.parse(event.data);
  // Handle real-time notifications
};
```

## Next Steps

1. Review the [Authentication Guide](./AUTHENTICATION_GUIDE.md) for detailed auth implementation
2. Check [Error Handling Guide](./ERROR_HANDLING_GUIDE.md) for comprehensive error handling
3. Explore [Data Models](./DATA_MODELS.md) for complete TypeScript interfaces
