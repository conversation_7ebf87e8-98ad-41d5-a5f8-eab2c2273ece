# Data Models & TypeScript Interfaces

## Overview

This document provides comprehensive TypeScript interfaces and data models for the Pantry Pal API. These interfaces ensure type safety and provide clear contracts for frontend development.

## Core Response Types

### API Response Structure

```typescript
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ErrorInfo;
  message?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}

interface ErrorInfo {
  code: string;
  message: string;
  details?: Record<string, any>;
}

interface PaginatedResponse<T = any> extends APIResponse<T[]> {
  pagination: PaginationMeta;
}

interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}
```

## Authentication Models

### User & Authentication

```typescript
interface User {
  id: string;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  profile_picture_url?: string;
  created_at: string;
  updated_at: string;
}

interface TokenPair {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: 'Bearer';
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
  confirm_password: string;
  first_name?: string;
  last_name?: string;
}

interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

interface UpdateProfileRequest {
  first_name?: string;
  last_name?: string;
  profile_picture_url?: string;
}
```

## Pantry Management Models

### Pantry Types

```typescript
type PantryRole = 'owner' | 'admin' | 'editor' | 'viewer';
type PantryMembershipStatus = 'pending' | 'active' | 'inactive';

interface Pantry {
  id: string;
  name: string;
  description?: string;
  owner_user_id: string;
  created_at: string;
  updated_at: string;
  is_owner: boolean;
  user_role?: string;
}

interface PantryMember {
  id: string;
  pantry_id: string;
  user_id: string;
  role: PantryRole;
  status: PantryMembershipStatus;
  joined_at: string;
  invited_by_user_id?: string;
  user_email?: string;
  user_first_name?: string;
  user_last_name?: string;
}

interface CreatePantryRequest {
  name: string;
  description?: string;
}

interface UpdatePantryRequest {
  name: string;
  description?: string;
}

interface InviteMemberRequest {
  email: string;
  role: PantryRole;
}

interface UpdateMemberRoleRequest {
  role: PantryRole;
}

interface TransferOwnershipRequest {
  new_owner_user_id: string;
}
```

### Pantry Locations

```typescript
type LocationType = 'pantry' | 'refrigerator' | 'freezer' | 'cabinet' | 'other';

interface PantryLocation {
  id: string;
  pantry_id: string;
  name: string;
  description?: string;
  location_type: LocationType;
  created_at: string;
  updated_at: string;
}

interface CreateLocationRequest {
  name: string;
  description?: string;
  location_type: LocationType;
}

interface UpdateLocationRequest {
  name: string;
  description?: string;
  location_type: LocationType;
}
```

## Product Catalog Models

### Categories

```typescript
interface Category {
  id: string;
  name: string;
  description?: string;
  parent_category_id?: string;
  created_at: string;
  updated_at: string;
  subcategories?: Category[];
  product_count?: number;
}

interface CreateCategoryRequest {
  name: string;
  description?: string;
  parent_category_id?: string;
}

interface UpdateCategoryRequest {
  name: string;
  description?: string;
  parent_category_id?: string;
}
```

### Units of Measure

```typescript
type UnitType = 'base' | 'derived';

interface UnitOfMeasure {
  id: string;
  name: string;
  symbol: string;
  unit_type: UnitType;
  base_unit_id?: string;
  conversion_factor?: number;
  created_at: string;
  updated_at: string;
}

interface CreateUnitRequest {
  name: string;
  symbol: string;
}

interface CreateDerivedUnitRequest {
  name: string;
  symbol: string;
  base_unit_id: string;
  conversion_factor: number;
}

interface UpdateUnitRequest {
  name: string;
  symbol: string;
}

interface UpdateConversionFactorRequest {
  conversion_factor: number;
}
```

### Products & Variants

```typescript
interface Product {
  id: string;
  name: string;
  description?: string;
  category_id: string;
  brand?: string;
  created_at: string;
  updated_at: string;
  category?: Category;
  variants?: ProductVariant[];
  variant_count?: number;
}

interface ProductVariant {
  id: string;
  product_id: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  unit_of_measure_id: string;
  package_size?: number;
  created_at: string;
  updated_at: string;
  product?: Product;
  unit_of_measure?: UnitOfMeasure;
}

interface CreateProductRequest {
  name: string;
  description?: string;
  category_id: string;
  brand?: string;
}

interface UpdateProductRequest {
  name: string;
  description?: string;
  category_id: string;
  brand?: string;
}

interface CreateProductVariantRequest {
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  unit_of_measure_id: string;
  package_size?: number;
}

interface UpdateProductVariantRequest {
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  unit_of_measure_id: string;
  package_size?: number;
}
```

## Inventory Management Models

### Inventory Items

```typescript
interface InventoryItem {
  id: string;
  pantry_id: string;
  location_id?: string;
  product_variant_id: string;
  quantity: number;
  unit_of_measure_id: string;
  purchase_date?: string;
  expiration_date?: string;
  purchase_price?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Populated relations
  product_variant?: ProductVariant;
  location?: PantryLocation;
  unit_of_measure?: UnitOfMeasure;
}

interface CreateInventoryItemRequest {
  product_variant_id: string;
  quantity: number;
  unit_of_measure_id: string;
  location_id?: string;
  purchase_date?: string;
  expiration_date?: string;
  purchase_price?: number;
  notes?: string;
}

interface UpdateInventoryItemRequest {
  quantity?: number;
  location_id?: string;
  expiration_date?: string;
  purchase_price?: number;
  notes?: string;
}

interface ConsumeInventoryRequest {
  consumed_quantity: number;
  notes?: string;
}

interface BulkCreateInventoryRequest {
  items: CreateInventoryItemRequest[];
}

interface BulkUpdateInventoryRequest {
  updates: Array<{
    id: string;
    quantity?: number;
    location_id?: string;
    expiration_date?: string;
    notes?: string;
  }>;
}

interface BulkConsumeInventoryRequest {
  consumptions: Array<{
    id: string;
    consumed_quantity: number;
    notes?: string;
  }>;
}

interface BulkDeleteInventoryRequest {
  item_ids: string[];
}
```

### Shopping Lists

```typescript
interface ShoppingListItem {
  product_variant_id: string;
  quantity: number;
  unit_of_measure_id: string;
  notes?: string;
  product_variant?: ProductVariant;
  unit_of_measure?: UnitOfMeasure;
}

interface GenerateShoppingListRequest {
  low_stock_threshold?: number;
  include_expired?: boolean;
  recipe_ids?: string[];
}

interface ShoppingListResponse {
  items: ShoppingListItem[];
  generated_at: string;
  criteria: {
    low_stock_threshold?: number;
    include_expired?: boolean;
    recipe_count?: number;
  };
}
```

## Recipe Management Models

### Recipe Types

```typescript
type RecipeDifficulty = 'easy' | 'medium' | 'hard';

interface Recipe {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  cuisine?: string;
  category?: string;
  difficulty?: RecipeDifficulty;
  prep_time?: number; // minutes
  cook_time?: number; // minutes
  servings: number;
  is_public: boolean;
  is_favorite: boolean;
  source?: string;
  notes?: string;
  tags: string[];
  created_at: string;
  updated_at: string;

  // Relations
  ingredients: RecipeIngredient[];
  instructions: RecipeInstruction[];
  nutrition?: RecipeNutrition;
  media?: RecipeMedia[];
}

interface RecipeIngredient {
  id: string;
  recipe_id: string;
  product_variant_id: string;
  quantity: number;
  unit_of_measure_id: string;
  notes?: string;
  is_optional: boolean;

  // Populated relations
  product_variant?: ProductVariant;
  unit_of_measure?: UnitOfMeasure;
}

interface RecipeInstruction {
  id: string;
  recipe_id: string;
  step_number: number;
  instruction: string;
  duration?: number; // minutes
  temperature?: number; // celsius
  notes?: string;
}

interface RecipeNutrition {
  id: string;
  recipe_id: string;
  calories?: number;
  protein?: number; // grams
  carbohydrates?: number; // grams
  fat?: number; // grams
  fiber?: number; // grams
  sugar?: number; // grams
  sodium?: number; // milligrams
}

interface RecipeMedia {
  id: string;
  recipe_id: string;
  media_type: 'image' | 'video';
  url: string;
  caption?: string;
  order_index: number;
}
```

### Recipe Requests

```typescript
interface CreateRecipeRequest {
  title: string;
  description?: string;
  cuisine?: string;
  category?: string;
  difficulty?: RecipeDifficulty;
  prep_time?: number;
  cook_time?: number;
  servings: number;
  is_public: boolean;
  is_favorite?: boolean;
  source?: string;
  notes?: string;
  tags?: string[];
  ingredients: CreateRecipeIngredientRequest[];
  instructions: CreateRecipeInstructionRequest[];
  nutrition?: CreateRecipeNutritionRequest;
}

interface CreateRecipeIngredientRequest {
  product_variant_id: string;
  quantity: number;
  unit_of_measure_id: string;
  notes?: string;
  is_optional?: boolean;
}

interface CreateRecipeInstructionRequest {
  step_number: number;
  instruction: string;
  duration?: number;
  temperature?: number;
  notes?: string;
}

interface CreateRecipeNutritionRequest {
  calories?: number;
  protein?: number;
  carbohydrates?: number;
  fat?: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
}

interface UpdateRecipeRequest {
  title?: string;
  description?: string;
  cuisine?: string;
  category?: string;
  difficulty?: RecipeDifficulty;
  prep_time?: number;
  cook_time?: number;
  servings?: number;
  is_public?: boolean;
  is_favorite?: boolean;
  source?: string;
  notes?: string;
  tags?: string[];
}

interface ScaleRecipeRequest {
  servings: number;
}

interface RateRecipeRequest {
  rating: number; // 1-5
  comment?: string;
  cooked_at?: string;
}

interface ConsumeRecipeIngredientsRequest {
  recipe_id: string;
  servings: number;
  notes?: string;
}
```

### Recipe Query & Response

```typescript
interface RecipeQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  cuisine?: string;
  category?: string;
  difficulty?: RecipeDifficulty;
  max_prep_time?: number;
  max_cook_time?: number;
  max_calories?: number;
  is_favorite?: boolean;
  is_public?: boolean;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

interface InventoryCheckRequest {
  pantry_id?: string;
}

interface IngredientAvailability {
  ingredient: RecipeIngredient;
  available_quantity: number;
  required_quantity: number;
  is_sufficient: boolean;
  missing_quantity?: number;
}

interface RecipeIngredientCheckResponse {
  recipe: Recipe;
  pantry_id: string;
  ingredients: IngredientAvailability[];
  can_make_recipe: boolean;
  missing_ingredients_count: number;
}
```

## Expiration Tracking Models

### Notification & Alert Types

```typescript
type NotificationChannelType = 'email' | 'push' | 'sms' | 'webhook';

interface NotificationChannel {
  type: NotificationChannelType;
  enabled: boolean;
  config: Record<string, any>;
}

interface QuietHours {
  enabled: boolean;
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  timezone: string;
}

interface AlertConfiguration {
  id: string;
  pantry_id?: string; // null for global config
  user_id: string;
  enabled: boolean;
  alert_days: number;
  warning_days: number;
  critical_days: number;
  min_value: number;
  channels: NotificationChannel[];
  quiet_hours?: QuietHours;
  category_filters: string[];
  created_at: string;
  updated_at: string;
}

interface AlertConfigurationRequest {
  enabled: boolean;
  alert_days: number;
  warning_days: number;
  critical_days: number;
  min_value: number;
  channels: NotificationChannel[];
  quiet_hours?: QuietHours;
  category_filters?: string[];
}

interface ExpirationTrackingRequest {
  alert_days: number;
  critical_days: number;
  category_filters?: string[];
}
```

### Expiring Items

```typescript
interface ExpiringItem {
  inventory_item: InventoryItem;
  days_until_expiration: number;
  alert_level: 'warning' | 'critical' | 'expired';
  estimated_value?: number;
}

interface ExpirationSummary {
  pantry_id: string;
  total_items: number;
  expiring_soon: number;
  expired: number;
  estimated_waste_value: number;
  items: ExpiringItem[];
  generated_at: string;
}
```

## Query Parameters & Filters

### Common Query Types

```typescript
interface BaseQueryParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

interface SearchQueryParams extends BaseQueryParams {
  search?: string;
}

interface InventoryQueryParams extends SearchQueryParams {
  location_id?: string;
  category_id?: string;
  product_id?: string;
  expiring_soon?: boolean;
  expired?: boolean;
  low_stock?: boolean;
  min_quantity?: number;
  max_quantity?: number;
}

interface ProductQueryParams extends SearchQueryParams {
  category_id?: string;
  brand?: string;
  has_variants?: boolean;
}

interface CategoryQueryParams extends SearchQueryParams {
  parent_category_id?: string;
  include_subcategories?: boolean;
}
```

## Utility Types

### Common Enums & Constants

```typescript
// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Error Codes
export const ERROR_CODES = {
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
} as const;

// Pantry Roles
export const PANTRY_ROLES = {
  OWNER: 'owner',
  ADMIN: 'admin',
  EDITOR: 'editor',
  VIEWER: 'viewer',
} as const;

// Location Types
export const LOCATION_TYPES = {
  PANTRY: 'pantry',
  REFRIGERATOR: 'refrigerator',
  FREEZER: 'freezer',
  CABINET: 'cabinet',
  OTHER: 'other',
} as const;
```

### Type Guards

```typescript
export function isAPIResponse<T>(obj: any): obj is APIResponse<T> {
  return obj && typeof obj.success === 'boolean' && typeof obj.timestamp === 'string';
}

export function isPaginatedResponse<T>(obj: any): obj is PaginatedResponse<T> {
  return isAPIResponse(obj) && obj.pagination && typeof obj.pagination.page === 'number';
}

export function isErrorResponse(obj: any): obj is APIResponse<never> {
  return isAPIResponse(obj) && !obj.success && obj.error;
}

export function hasValidationErrors(error: ErrorInfo): boolean {
  return error.code === ERROR_CODES.VALIDATION_FAILED &&
         error.details?.fields &&
         typeof error.details.fields === 'object';
}
```

## Usage Examples

### Type-Safe API Calls

```typescript
// Example service method with proper typing
class PantryService {
  async getPantries(): Promise<Pantry[]> {
    const response = await apiClient.get<APIResponse<Pantry[]>>('/pantries');
    if (!response.success || !response.data) {
      throw new Error('Failed to fetch pantries');
    }
    return response.data;
  }

  async createPantry(request: CreatePantryRequest): Promise<Pantry> {
    const response = await apiClient.post<APIResponse<Pantry>>('/pantries', request);
    if (!response.success || !response.data) {
      throw new Error('Failed to create pantry');
    }
    return response.data;
  }

  async getInventory(
    pantryId: string,
    params?: InventoryQueryParams
  ): Promise<PaginatedResponse<InventoryItem>> {
    const response = await apiClient.get<PaginatedResponse<InventoryItem>>(
      `/pantries/${pantryId}/inventory`,
      { params }
    );
    if (!response.success) {
      throw new Error('Failed to fetch inventory');
    }
    return response;
  }
}
```

### Form Validation Schemas

```typescript
import { z } from 'zod';

export const createPantrySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
});

export const createInventoryItemSchema = z.object({
  product_variant_id: z.string().uuid('Invalid product variant'),
  quantity: z.number().positive('Quantity must be positive'),
  unit_of_measure_id: z.string().uuid('Invalid unit of measure'),
  location_id: z.string().uuid('Invalid location').optional(),
  expiration_date: z.string().datetime().optional(),
  purchase_date: z.string().datetime().optional(),
  purchase_price: z.number().positive().optional(),
  notes: z.string().max(1000).optional(),
});

export const createRecipeSchema = z.object({
  title: z.string().min(3, 'Title too short').max(200, 'Title too long'),
  description: z.string().max(2000).optional(),
  cuisine: z.string().max(100).optional(),
  category: z.string().max(100).optional(),
  difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
  prep_time: z.number().min(0).max(1440).optional(),
  cook_time: z.number().min(0).max(1440).optional(),
  servings: z.number().min(1).max(100),
  is_public: z.boolean(),
  ingredients: z.array(z.object({
    product_variant_id: z.string().uuid(),
    quantity: z.number().positive(),
    unit_of_measure_id: z.string().uuid(),
    notes: z.string().optional(),
    is_optional: z.boolean().optional(),
  })).min(1, 'At least one ingredient required'),
  instructions: z.array(z.object({
    step_number: z.number().positive(),
    instruction: z.string().min(1, 'Instruction cannot be empty'),
    duration: z.number().min(0).optional(),
    temperature: z.number().optional(),
  })).min(1, 'At least one instruction required'),
});
```

```
