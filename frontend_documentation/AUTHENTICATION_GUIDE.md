# Authentication Guide

## Overview

The Pantry Pal API uses JWT (JSON Web Token) based authentication with access and refresh tokens. This guide provides comprehensive implementation details for frontend applications.

## Authentication Flow

### 1. Registration Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    
    User->>Frontend: Fill registration form
    Frontend->>API: POST /auth/register
    API->>API: Validate & create user
    API->>Frontend: Return user + tokens
    Frontend->>Frontend: Store tokens securely
    Frontend->>User: Redirect to dashboard
```

### 2. Login Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    
    User->>Frontend: Enter credentials
    Frontend->>API: POST /auth/login
    API->>API: Validate credentials
    API->>Frontend: Return user + tokens
    Frontend->>Frontend: Store tokens securely
    Frontend->>User: Redirect to dashboard
```

### 3. Token Refresh Flow

```mermaid
sequenceDiagram
    participant Frontend
    participant API
    
    Frontend->>API: API request with expired token
    API->>Frontend: 401 Unauthorized
    Frontend->>API: POST /auth/refresh
    API->>Frontend: New token pair
    Frontend->>Frontend: Update stored tokens
    Frontend->>API: Retry original request
```

## Token Management

### Token Structure

```typescript
interface TokenPair {
  access_token: string;
  refresh_token: string;
  expires_in: number; // seconds
  token_type: 'Bearer';
}

interface JWTPayload {
  user_id: string;
  email: string;
  type: 'access' | 'refresh';
  exp: number; // expiration timestamp
  iat: number; // issued at timestamp
  sub: string; // subject (user ID)
}
```

### Secure Token Storage

#### Web Applications (localStorage/sessionStorage)

```typescript
class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'pantry_pal_access_token';
  private static readonly REFRESH_TOKEN_KEY = 'pantry_pal_refresh_token';
  private static readonly TOKEN_EXPIRY_KEY = 'pantry_pal_token_expiry';

  static setTokens(tokens: TokenPair): void {
    const expiryTime = Date.now() + (tokens.expires_in * 1000);
    
    localStorage.setItem(this.ACCESS_TOKEN_KEY, tokens.access_token);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, tokens.refresh_token);
    localStorage.setItem(this.TOKEN_EXPIRY_KEY, expiryTime.toString());
  }

  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static isTokenExpired(): boolean {
    const expiry = localStorage.getItem(this.TOKEN_EXPIRY_KEY);
    if (!expiry) return true;
    
    return Date.now() >= parseInt(expiry);
  }

  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.TOKEN_EXPIRY_KEY);
  }
}
```

#### React Native (Secure Storage)

```typescript
import * as SecureStore from 'expo-secure-store';

class SecureTokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private static readonly TOKEN_EXPIRY_KEY = 'token_expiry';

  static async setTokens(tokens: TokenPair): Promise<void> {
    const expiryTime = Date.now() + (tokens.expires_in * 1000);
    
    await Promise.all([
      SecureStore.setItemAsync(this.ACCESS_TOKEN_KEY, tokens.access_token),
      SecureStore.setItemAsync(this.REFRESH_TOKEN_KEY, tokens.refresh_token),
      SecureStore.setItemAsync(this.TOKEN_EXPIRY_KEY, expiryTime.toString())
    ]);
  }

  static async getAccessToken(): Promise<string | null> {
    return await SecureStore.getItemAsync(this.ACCESS_TOKEN_KEY);
  }

  static async getRefreshToken(): Promise<string | null> {
    return await SecureStore.getItemAsync(this.REFRESH_TOKEN_KEY);
  }

  static async isTokenExpired(): Promise<boolean> {
    const expiry = await SecureStore.getItemAsync(this.TOKEN_EXPIRY_KEY);
    if (!expiry) return true;
    
    return Date.now() >= parseInt(expiry);
  }

  static async clearTokens(): Promise<void> {
    await Promise.all([
      SecureStore.deleteItemAsync(this.ACCESS_TOKEN_KEY),
      SecureStore.deleteItemAsync(this.REFRESH_TOKEN_KEY),
      SecureStore.deleteItemAsync(this.TOKEN_EXPIRY_KEY)
    ]);
  }
}
```

## HTTP Client Implementation

### Axios Implementation

```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

class APIClient {
  private client: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
  }> = [];

  constructor(baseURL: string) {
    this.client = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async (config) => {
        const token = TokenManager.getAccessToken();
        if (token && !TokenManager.isTokenExpired()) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then((token) => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.client(originalRequest);
            }).catch((err) => {
              return Promise.reject(err);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const refreshToken = TokenManager.getRefreshToken();
            if (!refreshToken) {
              throw new Error('No refresh token available');
            }

            const response = await this.refreshTokens(refreshToken);
            const { tokens } = response.data;
            
            TokenManager.setTokens(tokens);
            
            this.processQueue(null, tokens.access_token);
            
            originalRequest.headers.Authorization = `Bearer ${tokens.access_token}`;
            return this.client(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            TokenManager.clearTokens();
            // Redirect to login
            window.location.href = '/login';
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private processQueue(error: any, token: string | null): void {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });
    
    this.failedQueue = [];
  }

  private async refreshTokens(refreshToken: string): Promise<AxiosResponse> {
    return this.client.post('/auth/refresh', { refresh_token: refreshToken });
  }

  // Public methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }
}

// Create singleton instance
export const apiClient = new APIClient('http://localhost:8080/api/v1');
```

## Authentication Service

```typescript
interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
  confirm_password: string;
  first_name?: string;
  last_name?: string;
}

interface AuthResponse {
  success: boolean;
  data: {
    user: UserProfile;
    tokens: TokenPair;
  };
  message: string;
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    
    if (response.success && response.data.tokens) {
      TokenManager.setTokens(response.data.tokens);
    }
    
    return response;
  }

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/register', credentials);
    
    if (response.success && response.data.tokens) {
      TokenManager.setTokens(response.data.tokens);
    }
    
    return response;
  }

  async logout(): Promise<void> {
    const refreshToken = TokenManager.getRefreshToken();
    
    if (refreshToken) {
      try {
        await apiClient.post('/auth/logout', { refresh_token: refreshToken });
      } catch (error) {
        console.error('Logout error:', error);
      }
    }
    
    TokenManager.clearTokens();
  }

  async refreshToken(): Promise<TokenPair> {
    const refreshToken = TokenManager.getRefreshToken();
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiClient.post<AuthResponse>('/auth/refresh', {
      refresh_token: refreshToken
    });

    if (response.success && response.data.tokens) {
      TokenManager.setTokens(response.data.tokens);
      return response.data.tokens;
    }

    throw new Error('Failed to refresh token');
  }

  isAuthenticated(): boolean {
    const token = TokenManager.getAccessToken();
    return token !== null && !TokenManager.isTokenExpired();
  }

  getCurrentUser(): UserProfile | null {
    const token = TokenManager.getAccessToken();
    if (!token) return null;

    try {
      const payload = this.decodeJWT(token);
      return {
        id: payload.user_id,
        email: payload.email,
        // Additional user data should be fetched from API
      };
    } catch {
      return null;
    }
  }

  private decodeJWT(token: string): JWTPayload {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    
    return JSON.parse(jsonPayload);
  }
}

export const authService = new AuthService();

## React Integration

### Auth Context Provider

```typescript
import React, { createContext, useContext, useEffect, useState } from 'react';

interface AuthContextType {
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        if (authService.isAuthenticated()) {
          const currentUser = authService.getCurrentUser();
          setUser(currentUser);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    try {
      const response = await authService.login(credentials);
      setUser(response.data.user);
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    setIsLoading(true);
    try {
      const response = await authService.register(credentials);
      setUser(response.data.user);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await authService.logout();
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### Protected Route Component

```typescript
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from './AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <div>Loading...</div>; // Or your loading component
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};
```

### Login Component Example

```typescript
import React, { useState } from 'react';
import { useAuth } from './AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';

export const LoginForm: React.FC = () => {
  const [credentials, setCredentials] = useState({
    email: '',
    password: '',
  });
  const [error, setError] = useState<string | null>(null);

  const { login, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/dashboard';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      await login(credentials);
      navigate(from, { replace: true });
    } catch (err: any) {
      setError(err.response?.data?.error?.message || 'Login failed');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label htmlFor="email">Email:</label>
        <input
          id="email"
          type="email"
          value={credentials.email}
          onChange={(e) => setCredentials({ ...credentials, email: e.target.value })}
          required
        />
      </div>

      <div>
        <label htmlFor="password">Password:</label>
        <input
          id="password"
          type="password"
          value={credentials.password}
          onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
          required
        />
      </div>

      {error && <div className="error">{error}</div>}

      <button type="submit" disabled={isLoading}>
        {isLoading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
};
```

## Security Best Practices

### 1. Token Storage Security

* **Web**: Use `httpOnly` cookies when possible, fallback to localStorage
* **Mobile**: Use secure storage (Keychain/Keystore)
* **Never** store tokens in plain text or unsecured locations

### 2. Token Validation

* Always validate token expiration before making requests
* Implement automatic token refresh
* Handle refresh token expiration gracefully

### 3. Logout Security

* Always call logout endpoint to invalidate server-side sessions
* Clear all stored tokens and user data
* Redirect to login page immediately

### 4. Network Security

* Always use HTTPS in production
* Implement certificate pinning for mobile apps
* Validate SSL certificates

### 5. Error Handling

* Don't expose sensitive information in error messages
* Log authentication errors for monitoring
* Implement rate limiting on client side

## Testing Authentication

### Unit Tests Example

```typescript
import { authService } from './AuthService';
import { TokenManager } from './TokenManager';

jest.mock('./TokenManager');

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should login successfully', async () => {
    const mockResponse = {
      success: true,
      data: {
        user: { id: '1', email: '<EMAIL>' },
        tokens: {
          access_token: 'access_token',
          refresh_token: 'refresh_token',
          expires_in: 3600,
          token_type: 'Bearer'
        }
      }
    };

    // Mock API call
    jest.spyOn(apiClient, 'post').mockResolvedValue(mockResponse);

    const result = await authService.login({
      email: '<EMAIL>',
      password: 'password'
    });

    expect(result).toEqual(mockResponse);
    expect(TokenManager.setTokens).toHaveBeenCalledWith(mockResponse.data.tokens);
  });

  it('should handle login failure', async () => {
    const mockError = new Error('Invalid credentials');
    jest.spyOn(apiClient, 'post').mockRejectedValue(mockError);

    await expect(authService.login({
      email: '<EMAIL>',
      password: 'wrong_password'
    })).rejects.toThrow('Invalid credentials');

    expect(TokenManager.setTokens).not.toHaveBeenCalled();
  });
});
```

## Troubleshooting

### Common Issues

1. **Token Refresh Loop**: Ensure refresh token is valid and not expired
2. **CORS Issues**: Configure proper CORS headers on the backend
3. **Token Storage**: Verify tokens are being stored and retrieved correctly
4. **Network Timeouts**: Implement proper timeout handling
5. **Memory Leaks**: Clean up event listeners and timers

### Debug Tips

1. Enable network logging to inspect token headers
2. Check browser developer tools for storage issues
3. Monitor token expiration times
4. Verify API response formats match expected interfaces
5. Test authentication flow in different network conditions
```
