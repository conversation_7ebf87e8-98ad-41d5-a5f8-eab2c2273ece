{"swagger": "2.0", "info": {"description": "A comprehensive multi-tenant pantry management system for tracking inventory, managing shopping lists, and reducing food waste.", "title": "Pantry Pal API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "Pantry Pal API Support", "url": "http://www.pantrypal.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "2.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/auth/login": {"post": {"description": "Authenticate user with email and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User login", "parameters": [{"description": "Login credentials", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.LoginCredentials"}}], "responses": {"200": {"description": "Login successful", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Invalid credentials", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/auth/logout": {"post": {"description": "Logout user and revoke refresh token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User logout", "responses": {"200": {"description": "Logout successful", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/auth/refresh": {"post": {"description": "Generate new access token using refresh token from cookie", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Refresh access token", "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Refresh token not found or invalid", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/auth/register": {"post": {"description": "Create a new user account with email, username, and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Register a new user", "parameters": [{"description": "Registration credentials", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.RegisterCredentials"}}], "responses": {"201": {"description": "User registered successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "409": {"description": "Email or username already exists", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/expiration/alerts/global": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve expiration alert configuration for a pantry or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Get alert configuration", "responses": {"200": {"description": "Alert configuration retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Configure expiration alert settings for a pantry or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Configure expiration alerts", "parameters": [{"description": "Alert configuration", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.AlertConfigurationRequest"}}], "responses": {"200": {"description": "Alert configuration saved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/inventory/{itemId}": {"put": {"security": [{"BearerAuth": []}], "description": "Update an existing inventory item", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Update inventory item", "parameters": [{"type": "string", "description": "Inventory item ID", "name": "itemId", "in": "path", "required": true}, {"description": "Updated inventory item data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateInventoryItemRequest"}}], "responses": {"200": {"description": "Inventory item updated successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to item", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Inventory item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/inventory/{itemId}/consume": {"post": {"security": [{"BearerAuth": []}], "description": "Consume a specified quantity from an inventory item", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Consume inventory item", "parameters": [{"type": "string", "description": "Inventory item ID", "name": "itemId", "in": "path", "required": true}, {"description": "Consumption data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ConsumeInventoryRequest"}}], "responses": {"200": {"description": "Inventory item consumed successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or insufficient quantity", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to item", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Inventory item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all pantries accessible to the authenticated user with pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Get user's pantries", "parameters": [{"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}, {"type": "boolean", "description": "Only return owned pantries (default: false)", "name": "owner_only", "in": "query"}], "responses": {"200": {"description": "Pantries retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new pantry for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Create a new pantry", "parameters": [{"description": "Pantry creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreatePantryRequest"}}], "responses": {"201": {"description": "Pantry created successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "409": {"description": "Pantry name already exists", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a specific pantry by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Get pantry by ID", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}], "responses": {"200": {"description": "Pantry retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to this pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update pantry name and description", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Update pantry", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"description": "Pantry update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdatePantryRequest"}}], "responses": {"200": {"description": "Pantry updated successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no edit permission", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "409": {"description": "Pantry name already exists", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a pantry and all its associated data", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Delete pantry", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}], "responses": {"200": {"description": "Pantry deleted successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no delete permission", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/expiration/alerts": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve expiration alert configuration for a pantry or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Get alert configuration", "parameters": [{"type": "string", "description": "Pantry ID (optional for global config)", "name": "pantryId", "in": "path"}], "responses": {"200": {"description": "Alert configuration retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Configure expiration alert settings for a pantry or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Configure expiration alerts", "parameters": [{"type": "string", "description": "Pantry ID (optional for global config)", "name": "pantryId", "in": "path"}, {"description": "Alert configuration", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.AlertConfigurationRequest"}}], "responses": {"200": {"description": "Alert configuration saved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/expiration/track": {"post": {"security": [{"BearerAuth": []}], "description": "Track and analyze expiring items in a pantry with configurable thresholds", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Track expiring items", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"description": "Expiration tracking configuration", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ExpirationTrackingRequest"}}], "responses": {"200": {"description": "Expiring items tracked successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/inventory": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all inventory items in the specified pantry with pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Get pantry inventory", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Inventory retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID or query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new inventory item in the specified pantry", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Create inventory item", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"description": "Inventory item data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateInventoryItemRequest"}}], "responses": {"201": {"description": "Inventory item created successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry or product variant not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all recipes created by the authenticated user with pagination and filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Get user's recipes", "parameters": [{"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}, {"type": "string", "description": "Search in recipe title and description", "name": "search", "in": "query"}, {"type": "string", "description": "Filter by cuisine", "name": "cuisine", "in": "query"}, {"type": "string", "description": "Filter by category", "name": "category", "in": "query"}, {"type": "string", "description": "Filter by difficulty (easy, medium, hard, expert)", "name": "difficulty", "in": "query"}, {"type": "integer", "description": "Maximum preparation time in minutes", "name": "max_prep_time", "in": "query"}, {"type": "integer", "description": "Maximum cooking time in minutes", "name": "max_cook_time", "in": "query"}, {"type": "integer", "description": "Maximum calories per serving", "name": "max_calories", "in": "query"}, {"type": "boolean", "description": "Filter favorite recipes only", "name": "is_favorite", "in": "query"}, {"type": "string", "description": "Sort by field (created_at, updated_at, title, rating, cook_count)", "name": "sort_by", "in": "query"}, {"type": "string", "description": "Sort order (asc, desc)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "Recipes retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new recipe with ingredients, instructions, and optional media", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Create a new recipe", "parameters": [{"description": "Recipe creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateRecipeRequest"}}], "responses": {"201": {"description": "Recipe created successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/public": {"get": {"description": "Retrieve all public recipes with pagination and filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Get public recipes", "parameters": [{"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}, {"type": "string", "description": "Search in recipe title and description", "name": "search", "in": "query"}, {"type": "string", "description": "Filter by cuisine", "name": "cuisine", "in": "query"}, {"type": "string", "description": "Filter by category", "name": "category", "in": "query"}, {"type": "string", "description": "Filter by difficulty (easy, medium, hard, expert)", "name": "difficulty", "in": "query"}, {"type": "integer", "description": "Maximum preparation time in minutes", "name": "max_prep_time", "in": "query"}, {"type": "integer", "description": "Maximum cooking time in minutes", "name": "max_cook_time", "in": "query"}, {"type": "integer", "description": "Maximum calories per serving", "name": "max_calories", "in": "query"}, {"type": "string", "description": "Sort by field (created_at, updated_at, title, rating, cook_count)", "name": "sort_by", "in": "query"}, {"type": "string", "description": "Sort order (asc, desc)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "Public recipes retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/search": {"get": {"security": [{"BearerAuth": []}], "description": "Search for recipes by title, description, ingredients, or tags", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Search recipes", "parameters": [{"type": "string", "description": "Search query", "name": "q", "in": "query", "required": true}, {"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by cuisine", "name": "cuisine", "in": "query"}, {"type": "string", "description": "Filter by category", "name": "category", "in": "query"}, {"type": "string", "description": "Filter by difficulty (easy, medium, hard, expert)", "name": "difficulty", "in": "query"}, {"type": "integer", "description": "Maximum preparation time in minutes", "name": "max_prep_time", "in": "query"}, {"type": "integer", "description": "Maximum cooking time in minutes", "name": "max_cook_time", "in": "query"}, {"type": "integer", "description": "Maximum calories per serving", "name": "max_calories", "in": "query"}, {"type": "string", "description": "Sort by field (created_at, updated_at, title, rating, cook_count)", "name": "sort_by", "in": "query"}, {"type": "string", "description": "Sort order (asc, desc)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "Recipes found successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/{recipeId}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a specific recipe by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Get recipe by ID", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}], "responses": {"200": {"description": "Recipe retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid recipe ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to this recipe", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing recipe", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Update recipe", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}, {"description": "Recipe update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateRecipeRequest"}}], "responses": {"200": {"description": "Recipe updated successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - not recipe owner", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a recipe and all its associated data", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Delete recipe", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}], "responses": {"200": {"description": "Recipe deleted successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid recipe ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - not recipe owner", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/{recipeId}/check-inventory": {"post": {"security": [{"BearerAuth": []}], "description": "Check if recipe ingredients are available in pantry inventory", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Check ingredient availability", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}, {"description": "Inventory check data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.InventoryCheckRequest"}}], "responses": {"200": {"description": "Inventory availability checked successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to recipe or pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/{recipeId}/cook": {"post": {"security": [{"BearerAuth": []}], "description": "Mark a recipe as cooked to track cooking frequency", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Mark recipe as cooked", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> marked as cooked successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid recipe ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to recipe", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/{recipeId}/scale": {"post": {"security": [{"BearerAuth": []}], "description": "Scale a recipe to a different number of servings", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Scale recipe", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}, {"description": "Scaling data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ScaleRecipeRequest"}}], "responses": {"200": {"description": "Recipe scaled successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to recipe", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/users/change-password": {"post": {"security": [{"BearerAuth": []}], "description": "Change the current authenticated user's password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Change user password", "parameters": [{"description": "Password change data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ChangePasswordRequest"}}], "responses": {"200": {"description": "Password changed successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized or invalid current password", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/users/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve the current authenticated user's profile information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user profile", "responses": {"200": {"description": "Profile retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "User not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update the current authenticated user's profile information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Update user profile", "parameters": [{"description": "Profile update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateProfileRequest"}}], "responses": {"200": {"description": "Profile updated successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "User not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}}, "definitions": {"domain.AlertConfigurationRequest": {"type": "object", "required": ["channels"], "properties": {"alert_days": {"type": "integer", "maximum": 7, "minimum": 0}, "category_filters": {"type": "array", "items": {"type": "string"}}, "channels": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/domain.NotificationChannel"}}, "critical_days": {"type": "integer", "maximum": 1, "minimum": 0}, "enabled": {"type": "boolean"}, "min_value": {"type": "number", "minimum": 0}, "quiet_hours": {"$ref": "#/definitions/domain.QuietHours"}, "warning_days": {"type": "integer", "maximum": 30, "minimum": 1}}}, "domain.ChangePasswordRequest": {"type": "object", "required": ["confirm_password", "current_password", "new_password"], "properties": {"confirm_password": {"type": "string"}, "current_password": {"type": "string"}, "new_password": {"type": "string", "minLength": 8}}}, "domain.ConsumeInventoryRequest": {"type": "object", "required": ["consumed_quantity"], "properties": {"consumed_quantity": {"type": "number"}, "notes": {"type": "string", "maxLength": 500}}}, "domain.CreateInventoryItemRequest": {"type": "object", "required": ["product_variant_id", "quantity", "unit_of_measure_id"], "properties": {"expiration_date": {"type": "string"}, "location_id": {"type": "string"}, "notes": {"type": "string", "maxLength": 1000}, "product_variant_id": {"type": "string"}, "purchase_date": {"type": "string"}, "purchase_price": {"type": "number", "minimum": 0}, "quantity": {"type": "number"}, "unit_of_measure_id": {"type": "string"}}}, "domain.CreatePantryRequest": {"type": "object", "required": ["name"], "properties": {"description": {"type": "string", "maxLength": 500}, "name": {"type": "string", "maxLength": 100, "minLength": 1}}}, "domain.CreateRecipeIngredientRequest": {"type": "object", "required": ["name", "quantity"], "properties": {"is_garnish": {"type": "boolean"}, "is_optional": {"type": "boolean"}, "name": {"type": "string", "maxLength": 200, "minLength": 1}, "notes": {"type": "string", "maxLength": 500}, "preparation": {"type": "string", "maxLength": 200}, "product_variant_id": {"type": "string"}, "quantity": {"type": "number"}, "unit": {"type": "string", "maxLength": 50}, "unit_of_measure_id": {"type": "string"}}}, "domain.CreateRecipeInstructionRequest": {"type": "object", "required": ["instruction"], "properties": {"duration": {"type": "integer", "maximum": 1440, "minimum": 0}, "instruction": {"type": "string", "maxLength": 2000, "minLength": 10}, "temperature": {"type": "integer", "maximum": 500, "minimum": -50}, "tips": {"type": "string", "maxLength": 1000}, "title": {"type": "string", "maxLength": 200}}}, "domain.CreateRecipeNutritionRequest": {"type": "object", "properties": {"calories": {"type": "integer", "maximum": 10000, "minimum": 0}, "carbohydrates": {"type": "number", "maximum": 1000, "minimum": 0}, "fat": {"type": "number", "maximum": 1000, "minimum": 0}, "fiber": {"type": "number", "maximum": 1000, "minimum": 0}, "protein": {"type": "number", "maximum": 1000, "minimum": 0}, "serving_size": {"type": "string", "maxLength": 100}, "sodium": {"type": "number", "maximum": 10000, "minimum": 0}, "sugar": {"type": "number", "maximum": 1000, "minimum": 0}}}, "domain.CreateRecipeRequest": {"type": "object", "required": ["ingredients", "instructions", "servings", "title"], "properties": {"category": {"type": "string", "maxLength": 100}, "cook_time": {"type": "integer", "maximum": 1440, "minimum": 0}, "cuisine": {"type": "string", "maxLength": 100}, "description": {"type": "string", "maxLength": 1000}, "difficulty": {"enum": ["easy", "medium", "hard", "expert"], "allOf": [{"$ref": "#/definitions/domain.DifficultyLevel"}]}, "ingredients": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/domain.CreateRecipeIngredientRequest"}}, "instructions": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/domain.CreateRecipeInstructionRequest"}}, "is_public": {"type": "boolean"}, "notes": {"type": "string", "maxLength": 2000}, "nutrition": {"$ref": "#/definitions/domain.CreateRecipeNutritionRequest"}, "prep_time": {"type": "integer", "maximum": 1440, "minimum": 0}, "servings": {"type": "integer", "maximum": 100, "minimum": 1}, "source": {"type": "string", "maxLength": 500}, "tags": {"type": "array", "items": {"type": "string"}}, "title": {"type": "string", "maxLength": 200, "minLength": 3}}}, "domain.DifficultyLevel": {"type": "string", "enum": ["easy", "medium", "hard", "expert"], "x-enum-varnames": ["DifficultyEasy", "DifficultyMedium", "DifficultyHard", "Diff<PERSON><PERSON>yExpert"]}, "domain.ExpirationTrackingRequest": {"type": "object", "properties": {"alert_days": {"type": "integer", "maximum": 7, "minimum": 0}, "category_ids": {"type": "array", "items": {"type": "string"}}, "channels": {"type": "array", "items": {"$ref": "#/definitions/domain.NotificationChannel"}}, "critical_days": {"type": "integer", "maximum": 1, "minimum": 0}, "send_alerts": {"type": "boolean"}, "warning_days": {"type": "integer", "maximum": 30, "minimum": 1}}}, "domain.InventoryCheckRequest": {"type": "object", "properties": {"pantry_id": {"type": "string"}}}, "domain.LoginCredentials": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "minLength": 8}}}, "domain.NotificationChannel": {"type": "string", "enum": ["email", "telegram", "supabase", "webhook", "in_app"], "x-enum-varnames": ["NotificationChannelEmail", "NotificationChannelTelegram", "NotificationChannelSupabase", "NotificationChannelWebhook", "NotificationChannelInApp"]}, "domain.QuietHours": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "end_time": {"type": "string"}, "start_time": {"type": "string"}, "timezone": {"type": "string"}}}, "domain.RegisterCredentials": {"type": "object", "required": ["confirm_password", "email", "password", "username"], "properties": {"confirm_password": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string", "maxLength": 100}, "last_name": {"type": "string", "maxLength": 100}, "password": {"type": "string", "minLength": 8}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "domain.ScaleRecipeRequest": {"type": "object", "required": ["servings"], "properties": {"servings": {"type": "integer", "maximum": 100, "minimum": 1}}}, "domain.UpdateInventoryItemRequest": {"type": "object", "required": ["quantity"], "properties": {"expiration_date": {"type": "string"}, "location_id": {"type": "string"}, "notes": {"type": "string", "maxLength": 1000}, "purchase_date": {"type": "string"}, "purchase_price": {"type": "number", "minimum": 0}, "quantity": {"type": "number"}}}, "domain.UpdatePantryRequest": {"type": "object", "required": ["name"], "properties": {"description": {"type": "string", "maxLength": 500}, "name": {"type": "string", "maxLength": 100, "minLength": 1}}}, "domain.UpdateProfileRequest": {"type": "object", "properties": {"first_name": {"type": "string", "maxLength": 100}, "last_name": {"type": "string", "maxLength": 100}, "profile_picture_url": {"type": "string"}}}, "domain.UpdateRecipeRequest": {"type": "object", "properties": {"category": {"type": "string", "maxLength": 100}, "cook_time": {"type": "integer", "maximum": 1440, "minimum": 0}, "cuisine": {"type": "string", "maxLength": 100}, "description": {"type": "string", "maxLength": 1000}, "difficulty": {"enum": ["easy", "medium", "hard", "expert"], "allOf": [{"$ref": "#/definitions/domain.DifficultyLevel"}]}, "ingredients": {"type": "array", "items": {"$ref": "#/definitions/domain.CreateRecipeIngredientRequest"}}, "instructions": {"type": "array", "items": {"$ref": "#/definitions/domain.CreateRecipeInstructionRequest"}}, "is_favorite": {"type": "boolean"}, "is_public": {"type": "boolean"}, "notes": {"type": "string", "maxLength": 2000}, "nutrition": {"$ref": "#/definitions/domain.CreateRecipeNutritionRequest"}, "prep_time": {"type": "integer", "maximum": 1440, "minimum": 0}, "servings": {"type": "integer", "maximum": 100, "minimum": 1}, "source": {"type": "string", "maxLength": 500}, "tags": {"type": "array", "items": {"type": "string"}}, "title": {"type": "string", "maxLength": 200, "minLength": 3}}}, "handler.APIResponse": {"type": "object", "properties": {"data": {}, "error": {"$ref": "#/definitions/handler.ErrorInfo"}, "message": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": true}, "request_id": {"type": "string"}, "success": {"type": "boolean"}, "timestamp": {"type": "string"}}}, "handler.ErrorInfo": {"type": "object", "properties": {"code": {"type": "string"}, "details": {"type": "object", "additionalProperties": true}, "message": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}